🧠 Product Requirements Document (PRD)

Product Name: MindMirror (placeholder — feel free to rename)
Prepared For: Solo journaling users, therapy patients, introspective minds
Core Purpose: Help users track and reflect on mental/emotional states daily using structured therapy-guided prompts.

1. 🎯 Goals

Primary Goals

Enable users to journal based on prompts from therapy sessions

Visualize mental energy patterns (triggers, drains, cycles)

Encourage mindfulness with built-in breathwork exercise

Track sleep and label narratives to gain self-awareness

Secondary Goals

Gamify consistency (e.g., streaks, progress markers)

Allow exporting data (PDF/CSV)

Optionally share with therapist or accountability buddy

2. 🧩 Features

A. Daily Journal Entry

Each day, the user fills out:

Thoughts vs Energy

Text fields: Energizing thoughts / Draining thoughts

Existence vs Drain

Text fields: Felt more alive with... / Felt more drained by...

Sleep Cycle

Inputs: Hours slept, Sleep/Wake time, Quality slider (1–10), Dream notes

You vs Others' Labels

Label assigned to you

Their story

Your truth

Box Breathing Exercise

Guided 4–4–4–4 timer (Inhale, Hold, Exhale, Hold)

State before/after (text fields)

B. Journal History

Calendar or list view of past entries

Filter/search by emotion tags (optional)

Graphs:

Energy trend over time

Sleep quality chart

Most frequent labels

C. User Account

Sign up/login (email or OAuth)

Profile: name, timezone, journal preferences

Export data (PDF/CSV/JSON)

Dark/light mode toggle

D. Admin Tools (if needed)

Prompt manager

Breathing guide updater

Feedback/report dashboard

3. 📐 UX Flow

User lands on homepage
→ Overview of purpose → Login/Signup

Dashboard
→ Button: "Start Today's Reflection"
→ Show today's date and current streak
→ Optional daily quote/intention

Journal Flow (Multi-step form)
→ Each prompt as a separate screen
→ Auto-save progress
→ On submit, confirmation + gentle encouragement

Box Breathing Timer
→ Simple animated timer for 4s in → 4s hold → 4s out → 4s hold
→ Optional sound
→ Reflection entry afterward

Journal History
→ Scrollable list/calendar
→ Tap to expand any day
→ Data export/download from here

4. 🏗️ Tech Stack (Suggested)

4. 🏗️ Tech Stack (Suggested)

Layer Tech Suggestion
Frontend React - Bun
UI TailwindCSS / shadcn
State Mgmt Zustand 
Auth Clerk/Convex Auth
Backend Convex functions 
DB Convex

5. 🧪 MVP Scope

Only include:

Core journaling flow (multi-step form)

Box breathing guide

Journal history with local storage or basic DB

Light/dark mode

No auth required in MVP (can be added later)

6. 📊 Future Features (Post-MVP)

Mood tagging & emoji reactions

Streak & habit dashboard

Therapist collaboration portal

AI-based reflection summary

Push/email reminders

Mobile app (PWA or native)

7. 🪄 Design Tone & UX Philosophy

Emotionally safe: calming color palette, subtle animations

Distraction-free: zero clutter, focus mode on journal

Privacy-first: no tracking, encrypted storage recommended

Poetic UI: e.g., placeholder text like “Write the weight or the wonder...” instead of “Type here…”

8. 🎨 Wireframes (Described)

Let me know if you want me to generate proper mockups/images.

Homepage

+------------------------------+ | MindMirror 🧠 | | [Start Journaling] | | "Your mind is a mirror. | | Let it reflect, not react."| +------------------------------+ 

Journal Entry (Step 1/5)

[Thoughts that energized you today] [Thoughts that drained you] [Next] → 

Box Breathing

🫁 Box Breathing Guide [Inhale...] [Hold...] [Exhale...] [Hold...] [Start Timer] [Reflection] How do you feel now? [Type here...] 

✅ Success Metrics

✅ User completes 3+ entries in a week

✅ Retention after 7 days

✅ Export/download feature used

✅ Box breathing engaged regularly

✅ Positive feedback via form
